import chalk from 'chalk';

/**
 * Logger class with colorful console output using chalk
 */
class Logger {
  constructor(options = {}) {
    this.showTimestamp = options.showTimestamp !== false; // default true
    this.showLevel = options.showLevel !== false; // default true
    this.prefix = options.prefix || '';
  }

  /**
   * Get formatted timestamp
   * @returns {string} Formatted timestamp
   */
  getTimestamp() {
    return new Date().toISOString();
  }

  /**
   * Format log message with optional timestamp and level
   * @param {string} level - Log level
   * @param {string} message - Log message
   * @returns {string} Formatted message
   */
  formatMessage(level, message) {
    let formatted = '';
    
    if (this.showTimestamp) {
      formatted += chalk.gray(`[${this.getTimestamp()}]`) + ' ';
    }
    
    if (this.showLevel) {
      formatted += level + ' ';
    }
    
    if (this.prefix) {
      formatted += chalk.cyan(`[${this.prefix}]`) + ' ';
    }
    
    formatted += message;
    
    return formatted;
  }

  /**
   * Log info message in blue
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  info(message, ...args) {
    const level = chalk.blue('INFO');
    console.log(this.formatMessage(level, message), ...args);
  }

  /**
   * Log success message in green
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  success(message, ...args) {
    const level = chalk.green('SUCCESS');
    console.log(this.formatMessage(level, message), ...args);
  }

  /**
   * Log warning message in yellow
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  warn(message, ...args) {
    const level = chalk.yellow('WARN');
    console.warn(this.formatMessage(level, message), ...args);
  }

  /**
   * Log error message in red
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  error(message, ...args) {
    const level = chalk.red('ERROR');
    console.error(this.formatMessage(level, message), ...args);
  }

  /**
   * Log debug message in magenta
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  debug(message, ...args) {
    const level = chalk.magenta('DEBUG');
    console.log(this.formatMessage(level, message), ...args);
  }

  /**
   * Log message with custom color
   * @param {string} color - Chalk color name
   * @param {string} level - Log level
   * @param {string} message - Message to log
   * @param {...any} args - Additional arguments
   */
  custom(color, level, message, ...args) {
    const coloredLevel = chalk[color] ? chalk[color](level) : level;
    console.log(this.formatMessage(coloredLevel, message), ...args);
  }

  /**
   * Create a new logger instance with a prefix
   * @param {string} prefix - Prefix for the logger
   * @returns {Logger} New logger instance
   */
  withPrefix(prefix) {
    return new Logger({
      showTimestamp: this.showTimestamp,
      showLevel: this.showLevel,
      prefix: this.prefix ? `${this.prefix}:${prefix}` : prefix
    });
  }

  /**
   * Log a separator line
   * @param {string} char - Character to use for separator (default: '-')
   * @param {number} length - Length of separator (default: 50)
   */
  separator(char = '-', length = 50) {
    console.log(chalk.gray(char.repeat(length)));
  }

  /**
   * Log a header with styling
   * @param {string} text - Header text
   */
  header(text) {
    this.separator('=');
    console.log(chalk.bold.cyan(text.toUpperCase()));
    this.separator('=');
  }
}

export default Logger;
