// import chalk from 'chalk'

import Logger from "./logger";

// const log = console.log
// chalk.level = 1 // Use colours in the VS Code Debug Window
// log(chalk.yellow('Welcome to the app!'))
// for (var i = 0; i <= 10; i++) {
// 	log(chalk.white('Attempting to connect to endpoint, attempt ' + i))
// }

// log(chalk.green('Connection established! Sending statistics'))
// log(chalk.red('Not all statistics are available...'))
// log(chalk.redBright('Endpoint disconnected before all results were received'))
// log(chalk.magenta('All finished'))


const logger = new Logger()

logger.header('Logger Demo')

logger.info('This is an info message')
logger.success('Operation completed successfully!')
logger.warn('This is a warning message')
logger.error('This is an error message')
logger.debug('This is a debug message')

logger.separator()

// // Create logger with custom options
// const customLogger = new Logger({
//   showTimestamp: true,
//   showLevel: true,
//   prefix: 'MyApp'
// });

